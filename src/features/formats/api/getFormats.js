import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения списка форматов
const getFormats = (eventCityId) => {
  return api.get(`/api/event/event_format/${eventCityId}`)
}

// Ключ запроса для React Query
export const GET_FORMATS_KEY = 'formats'

// Хук React Query для получения списка форматов
export const useGetFormats = (eventCityId) => {
  return useQuery({
    queryKey: [GET_FORMATS_KEY, eventCityId],
    queryFn: () => getFormats(eventCityId),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    enabled: !!eventCityId, // Запрос выполняется только если есть eventCityId
  })
}
