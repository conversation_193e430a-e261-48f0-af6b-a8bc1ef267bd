import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'

const api = createAPI()

// Функция для получения списка компаний
const getCompanies = () => {
  return api.post('/api/companies', {})
}

// Ключ запроса для React Query
export const GET_COMPANIES_KEY = 'companies'

// Хук React Query для получения списка компаний
export const useGetCompanies = () => {
  return useQuery({
    queryKey: [GET_COMPANIES_KEY],
    queryFn: () => getCompanies(),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
  })
}
