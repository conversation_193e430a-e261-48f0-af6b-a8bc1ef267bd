import { useMutation } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { useToastContext } from '@/hooks/useToastContext'
import { queryClient } from '@/lib/queryClient'

import { GET_FREE_TEAMS_WITHOUT_TICKETS_KEY } from './getFreeTeamsWithoutTickets'
import { GET_USED_NUMBERS_KEY } from './getUsedNumbers'

const api = createAPI()

// Функция для создания команды
const createTeam = (teamData) => {
  return api.post('/api/team', teamData)
}

// Хук React Query для создания команды
export const useCreateTeam = () => {
  const toast = useToastContext()

  return useMutation({
    mutationFn: createTeam,
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Инвалидируем кэш для обновления списков команд
        queryClient.invalidateQueries([GET_USED_NUMBERS_KEY])
        queryClient.invalidateQueries([GET_FREE_TEAMS_WITHOUT_TICKETS_KEY])
        toast.success({ message: 'Команда успешно создана!' })
      }
    },
    onError: (error) => {
      toast.error({
        message: error?.response?.data?.message || 'Ошибка при создании команды',
      })
    },
  })
}
