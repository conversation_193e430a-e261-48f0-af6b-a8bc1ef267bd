import { useMutation } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { queryClient } from '@/lib/queryClient'

import { GET_FREE_TEAMS_WITHOUT_TICKETS_KEY } from './getFreeTeamsWithoutTickets'
import { GET_USED_NUMBERS_KEY } from './getUsedNumbers'

const api = createAPI()

// Функция для создания команды
const createTeam = (teamData) => {
  return api.post('/api/team', teamData)
}

// Хук React Query для создания команды
export const useCreateTeam = (options = {}) => {
  return useMutation({
    mutationFn: createTeam,
    onSuccess: (data, variables, context) => {
      if (data?.status === 200) {
        // Инвалидируем кэш для обновления списков команд
        queryClient.invalidateQueries([GET_USED_NUMBERS_KEY])
        queryClient.invalidateQueries([GET_FREE_TEAMS_WITHOUT_TICKETS_KEY])
      }
      // Вызываем дополнительный колбэк, если он передан
      if (options.onSuccess) {
        options.onSuccess(data, variables, context)
      }
    },
    onError: (error, variables, context) => {
      // Вызываем дополнительный колбэк ошибки, если он передан
      if (options.onError) {
        options.onError(error, variables, context)
      }
    },
  })
}
