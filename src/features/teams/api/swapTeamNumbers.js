import { useMutation } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { queryClient } from '@/lib/queryClient'

import { GET_FREE_TEAMS_WITHOUT_TICKETS_KEY } from './getFreeTeamsWithoutTickets'
import { GET_USED_NUMBERS_KEY } from './getUsedNumbers'

const api = createAPI()

// Функция для смены номеров команд между собой
const swapTeamNumbers = ({ firstTeamPublicId, secondTeamPublicId }) => {
  return api.put('/api/volunteer/swap/teams/numbers', {
    first_team_public_id: firstTeamPublicId,
    second_team_public_id: secondTeamPublicId,
  })
}

// Хук React Query для смены номеров команд между собой
export const useSwapTeamNumbers = () => {
  return useMutation({
    mutationFn: swapTeamNumbers,
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Инвалидируем кэш для обновления списков команд
        queryClient.invalidateQueries([GET_USED_NUMBERS_KEY])
        queryClient.invalidateQueries([GET_FREE_TEAMS_WITHOUT_TICKETS_KEY])
      }
    },
  })
}
