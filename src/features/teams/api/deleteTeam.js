import { useMutation } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { queryClient } from '@/lib/queryClient'

import { GET_FREE_TEAMS_WITHOUT_TICKETS_KEY } from './getFreeTeamsWithoutTickets'
import { GET_USED_NUMBERS_KEY } from './getUsedNumbers'

const api = createAPI()

// Функция для удаления команды
const deleteTeam = (publicId) => {
  return api.delete(`/api/team/${publicId}`)
}

// Хук React Query для удаления команды
export const useDeleteTeam = () => {
  return useMutation({
    mutationFn: deleteTeam,
    onSuccess: (data) => {
      if (data?.status === 200) {
        // Инвалидируем кэш для обновления списков команд
        queryClient.invalidateQueries([GET_USED_NUMBERS_KEY])
        queryClient.invalidateQueries([GET_FREE_TEAMS_WITHOUT_TICKETS_KEY])
      }
    },
  })
}
